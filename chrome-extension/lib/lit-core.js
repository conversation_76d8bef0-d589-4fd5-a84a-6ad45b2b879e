/**
 * Version simplifiée de Lit pour les extensions Chrome
 * Compatible avec les CSP strictes
 */

// Base class pour les composants
export class LitElement extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
    this._updateScheduled = false;
    this._properties = new Map();
    
    // Initialiser les propriétés définies
    if (this.constructor.properties) {
      Object.keys(this.constructor.properties).forEach(prop => {
        this._properties.set(prop, undefined);
        
        // Créer getter/setter pour chaque propriété
        Object.defineProperty(this, `_${prop}`, {
          value: undefined,
          writable: true
        });
        
        Object.defineProperty(this, prop, {
          get() {
            return this[`_${prop}`];
          },
          set(value) {
            const oldValue = this[`_${prop}`];
            this[`_${prop}`] = value;
            this._properties.set(prop, value);
            this.requestUpdate(prop, oldValue);
          }
        });
      });
    }
  }

  connectedCallback() {
    this.requestUpdate();
  }

  requestUpdate(property, oldValue) {
    if (!this._updateScheduled) {
      this._updateScheduled = true;
      Promise.resolve().then(() => {
        this._updateScheduled = false;
        this.performUpdate();
      });
    }
  }

  performUpdate() {
    const result = this.render();
    this.shadowRoot.innerHTML = '';
    
    // Ajouter les styles
    if (this.constructor.styles) {
      const styleEl = document.createElement('style');
      styleEl.textContent = this.constructor.styles;
      this.shadowRoot.appendChild(styleEl);
    }
    
    // Ajouter le contenu
    if (typeof result === 'string') {
      const div = document.createElement('div');
      div.innerHTML = result;
      while (div.firstChild) {
        this.shadowRoot.appendChild(div.firstChild);
      }
    } else if (result instanceof DocumentFragment) {
      this.shadowRoot.appendChild(result);
    } else if (result instanceof HTMLElement) {
      this.shadowRoot.appendChild(result);
    }
    
    // Attacher les event listeners
    this.attachEventListeners();
  }

  render() {
    return '';
  }

  attachEventListeners() {
    // Override dans les sous-classes si nécessaire
  }

  // Méthode utilitaire pour créer des éléments
  createElement(tag, attributes = {}, children = []) {
    const element = document.createElement(tag);
    
    Object.entries(attributes).forEach(([key, value]) => {
      if (key.startsWith('on') && typeof value === 'function') {
        element.addEventListener(key.slice(2).toLowerCase(), value);
      } else if (key === 'className') {
        element.className = value;
      } else {
        element.setAttribute(key, value);
      }
    });
    
    children.forEach(child => {
      if (typeof child === 'string') {
        element.appendChild(document.createTextNode(child));
      } else if (child instanceof HTMLElement) {
        element.appendChild(child);
      }
    });
    
    return element;
  }
}

// Template helper functions
export function html(strings, ...values) {
  let result = '';
  for (let i = 0; i < strings.length; i++) {
    result += strings[i];
    if (i < values.length) {
      const value = values[i];
      if (typeof value === 'function') {
        // Pour les event handlers, on les stocke pour les attacher plus tard
        result += `data-event-${i}="true"`;
      } else {
        result += value !== undefined && value !== null ? value : '';
      }
    }
  }
  return result;
}

export function css(strings, ...values) {
  let result = '';
  for (let i = 0; i < strings.length; i++) {
    result += strings[i];
    if (i < values.length) {
      result += values[i];
    }
  }
  return result;
}

// Utilitaires pour les événements
export function createEventHandler(handler) {
  return function(event) {
    handler.call(this, event);
  };
}
