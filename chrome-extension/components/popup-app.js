import { LitElement, html, css } from 'lit';
import './webhost-card.js';
import './hosting-info.js';
import './tech-info.js';
import './links-grid.js';

export class PopupApp extends LitElement {
  static properties = {
    currentUrl: { type: String },
    webHost: { type: Object },
    links: { type: Array },
    loading: { type: Boolean },
    error: { type: String },
    noMatch: { type: Boolean }
  };

  static styles = css`
    :host {
      display: block;
      padding: 16px;
    }

    .loading {
      text-align: center;
      padding: 32px 16px;
      color: #666;
    }

    .loading::before {
      content: '⏳';
      display: block;
      font-size: 24px;
      margin-bottom: 8px;
    }

    .error {
      background: linear-gradient(135deg, #ff6b6b, #ee5a52);
      color: white;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
      text-align: center;
    }

    .error::before {
      content: '⚠️';
      display: block;
      font-size: 20px;
      margin-bottom: 4px;
    }

    .no-match {
      text-align: center;
      padding: 32px 16px;
      color: #666;
    }

    .no-match::before {
      content: '🔍';
      display: block;
      font-size: 32px;
      margin-bottom: 12px;
      opacity: 0.5;
    }

    .cache-info {
      font-size: 10px;
      color: #999;
      text-align: center;
      padding: 8px;
      background: #f8f9fa;
      border-top: 1px solid #e9ecef;
      margin: 0 -16px -16px -16px;
    }

    .clear-cache-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      cursor: pointer;
    }

    .clear-cache-link:hover {
      text-decoration: underline;
    }
  `;

  constructor() {
    super();
    this.loading = true;
    this.error = '';
    this.noMatch = false;
    this.webHost = null;
    this.links = [];
    this.currentUrl = '';
  }

  async connectedCallback() {
    super.connectedCallback();
    await this.init();
  }

  async init() {
    try {
      // Obtenir l'URL de l'onglet actif
      this.currentUrl = await this.getCurrentTabUrl();
      this.updateCurrentUrlDisplay();

      // Récupérer les données des webhosts
      const webHosts = await this.fetchWebHosts();
      
      // Trouver le webhost correspondant
      const matchingWebHost = this.findMatchingWebHost(this.currentUrl, webHosts);

      if (matchingWebHost) {
        this.webHost = matchingWebHost;
        this.links = this.extractLinksFromWebHost(matchingWebHost);
        this.loading = false;
      } else {
        this.noMatch = true;
        this.loading = false;
      }
    } catch (error) {
      this.error = error.message;
      this.loading = false;
    }
  }

  render() {
    if (this.loading) {
      return html`<div class="loading">Recherche des liens...</div>`;
    }

    if (this.error) {
      return html`
        <div class="error">
          <strong>Erreur:</strong> ${this.error}
        </div>
      `;
    }

    if (this.noMatch) {
      return html`
        <div class="no-match">
          <div>Aucun webhost trouvé pour cette URL</div>
          ${this.renderDebugControls()}
        </div>
      `;
    }

    return html`
      <webhost-card .webHost=${this.webHost}></webhost-card>
      
      ${this.webHost?.configuration ? 
        html`<hosting-info .configuration=${this.webHost.configuration}></hosting-info>` : ''}
      
      ${this.webHost?.service ? 
        html`<tech-info 
          .serviceDetails=${this.webHost.service.details} 
          .serviceRepository=${this.webHost.service.repository}>
        </tech-info>` : ''}
      
      <links-grid .links=${this.links}></links-grid>
      
      ${this.renderDebugControls()}
    `;
  }

  renderDebugControls() {
    if (!window.CONFIG?.DEBUG) return '';
    
    return html`
      <div class="cache-info">
        💾 Données mises en cache (1h) • 
        <span class="clear-cache-link" @click=${this.clearCache}>Vider le cache</span>
      </div>
    `;
  }

  async getCurrentTabUrl() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab.url;
  }

  updateCurrentUrlDisplay() {
    const urlElement = document.getElementById('currentUrl');
    if (urlElement) {
      urlElement.textContent = this.currentUrl;
    }
  }

  async clearCache() {
    try {
      await chrome.runtime.sendMessage({ action: 'clearCache' });
      window.location.reload();
    } catch (error) {
      console.error('Erreur lors du vidage du cache:', error);
    }
  }

  // Méthodes utilitaires (à déplacer dans des services séparés si nécessaire)
  async fetchWebHosts() {
    // Implémentation similaire à l'original
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getCachedWebHosts' });
      if (response && response.webHosts) {
        return response.webHosts;
      }
      return await this.fetchWebHostsDirect();
    } catch (error) {
      return await this.fetchWebHostsDirect();
    }
  }

  async fetchWebHostsDirect() {
    const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json'
      },
      timeout: CONFIG.API_TIMEOUT
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data.webHosts || [];
  }

  findMatchingWebHost(currentUrl, webHosts) {
    const currentDomain = this.extractDomain(currentUrl);
    
    for (const webHost of webHosts) {
      // Vérifier les URLs principales
      if (webHost.urls && webHost.urls.length > 0) {
        for (const urlObj of webHost.urls) {
          const webhostDomain = this.extractDomain(urlObj.url);
          if (webhostDomain === currentDomain) {
            return webHost;
          }
        }
      }

      // Vérifier les URLs associées
      if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
        for (const associatedUrl of webHost.associatedUrls) {
          const associatedDomain = this.extractDomain(associatedUrl.url);
          if (associatedDomain === currentDomain) {
            return webHost;
          }
        }
      }
    }

    return null;
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return '';
    }
  }

  extractLinksFromWebHost(webHost) {
    const links = [];

    // GitLab
    if (webHost.gitlabRemoteUrl) {
      const gitlabUrl = this.convertGitUrlToHttp(webHost.gitlabRemoteUrl);
      links.push({
        type: 'gitlab',
        title: 'GitLab',
        url: gitlabUrl,
        originalUrl: webHost.gitlabRemoteUrl
      });
    }

    // Confluence
    if (webHost.confluenceUrl) {
      links.push({ 
        type: 'confluence', 
        title: 'Confluence', 
        url: webHost.confluenceUrl 
      });
    }

    // Base de données
    const databaseUrl = webHost.databaseUrl || webHost.databaseExplorerUrl;
    if (databaseUrl) {
      links.push({ 
        type: 'database', 
        title: 'Base de données', 
        url: databaseUrl 
      });
    }

    // URLs associées
    if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
      webHost.associatedUrls.forEach((associatedUrl) => {
        links.push({
          type: 'associated',
          title: 'Lien associé',
          url: associatedUrl.url
        });
      });
    }

    // Services Docker
    if (webHost.service) {
      links.push({
        type: 'docker',
        title: 'TDB Swarm',
        label: 'Ouvrir dans TDB Swarm',
        url: 'https://tdb-swarm.int.alienor.net#' + webHost.service.id
      });

      if (webHost.service.links?.logs) {
        links.push({
          type: 'docker',
          title: 'Logs Docker',
          label: 'Ouvrir dans Swarmpit',
          url: webHost.service.links.logs
        });
      }

      if (webHost.service.tasks && webHost.service.tasks.length > 0) {
        const firstTask = webHost.service.tasks[0];
        if (firstTask.dockerToolsCommand) {
          links.push({
            type: 'docker',
            title: 'Docker Tools',
            url: firstTask.dockerToolsCommand
          });
        }
      }
    }

    return links;
  }

  convertGitUrlToHttp(gitUrl) {
    if (!gitUrl || gitUrl.startsWith('http')) return gitUrl;
    if (!gitUrl.startsWith('git@')) return gitUrl;

    try {
      const withoutPrefix = gitUrl.substring(4);
      const colonIndex = withoutPrefix.indexOf(':');
      if (colonIndex === -1) return gitUrl;

      const hostname = withoutPrefix.substring(0, colonIndex);
      let path = withoutPrefix.substring(colonIndex + 1);

      if (path.match(/^\d+\//)) {
        const slashIndex = path.indexOf('/');
        if (slashIndex !== -1) {
          path = path.substring(slashIndex + 1);
        }
      }

      if (path.endsWith('.git')) {
        path = path.substring(0, path.length - 4);
      }

      return `https://${hostname}/${path}`;
    } catch (error) {
      return gitUrl;
    }
  }
}

customElements.define('popup-app', PopupApp);
