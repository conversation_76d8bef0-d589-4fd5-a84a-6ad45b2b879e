import { LitElement, html, css } from 'lit';

export class LinkCard extends LitElement {
  static properties = {
    link: { type: Object }
  };

  static styles = css`
    .link-card {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 8px 10px;
      transition: all 0.2s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .link-card:hover {
      border-color: #667eea;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    .link-main {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
    }

    .link-icon {
      font-size: 14px;
      margin-right: 8px;
      width: 16px;
      text-align: center;
      flex-shrink: 0;
    }

    .link-info {
      flex: 1;
      min-width: 0;
    }

    .link-title {
      font-weight: 500;
      color: #333;
      font-size: 12px;
      margin-bottom: 1px;
    }

    .link-url {
      color: #667eea;
      text-decoration: none;
      font-size: 10px;
      word-break: break-all;
      display: block;
      opacity: 0.8;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .link-url:hover {
      text-decoration: underline;
    }

    .status-indicator {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      flex-shrink: 0;
      margin-left: 8px;
    }

    .status-ok {
      background-color: #28a745;
      box-shadow: 0 0 4px rgba(40, 167, 69, 0.4);
    }

    .status-error {
      background-color: #dc3545;
      box-shadow: 0 0 4px rgba(220, 53, 69, 0.4);
    }

    .status-unknown {
      background-color: #6c757d;
    }
  `;

  render() {
    if (!this.link) return html``;

    return html`
      <div class="link-card" @click=${this._handleClick}>
        <div class="link-main">
          <span class="link-icon">${this._getIcon()}</span>
          <div class="link-info">
            <div class="link-title">${this.link.title}</div>
            <a class="link-url" 
               href=${this.link.url} 
               target="_blank"
               title=${this._getTooltip()}
               @click=${this._handleLinkClick}>
              ${this._getLinkText()}
            </a>
          </div>
        </div>
        ${this.link.status ? this._renderStatusIndicator() : ''}
      </div>
    `;
  }

  _getIcon() {
    const icons = {
      gitlab: '🦊',
      confluence: '📖',
      database: '🗄️',
      url: '🌐',
      associated: '🔗',
      docker: '🐳'
    };
    return icons[this.link.type] || '🔗';
  }

  _getLinkText() {
    if (this.link.label) {
      return this.link.label;
    }
    
    if (this.link.type === 'docker') {
      return 'Ouvrir avec Docker Tools';
    }
    
    if (this.link.originalUrl && this.link.originalUrl !== this.link.url) {
      return this.link.originalUrl;
    }
    
    return this.link.url;
  }

  _getTooltip() {
    if (this.link.type === 'docker') {
      return `Clique pour ouvrir: ${this.link.url}`;
    }
    
    if (this.link.originalUrl && this.link.originalUrl !== this.link.url) {
      return `Clique pour ouvrir: ${this.link.url}`;
    }
    
    return this.link.url;
  }

  _renderStatusIndicator() {
    const statusClass = this.link.status === 'OK' ? 'status-ok' : 
                       this.link.status === 'ERROR' || this.link.status === 'TIMEOUT' ? 'status-error' : 
                       'status-unknown';
    
    const title = this.link.status === 'OK' ? 'Service disponible' :
                  this.link.status === 'ERROR' || this.link.status === 'TIMEOUT' ? 'Service indisponible' :
                  'Statut inconnu';

    return html`<span class="status-indicator ${statusClass}" title=${title}></span>`;
  }

  _handleClick(e) {
    if (e.target.tagName !== 'A') {
      window.open(this.link.url, '_blank');
    }
  }

  _handleLinkClick(e) {
    e.stopPropagation();
  }
}

customElements.define('link-card', LinkCard);
