import {defineManifest} from '@crxjs/vite-plugin'
import pkg from './package.json'

export default defineManifest({
    manifest_version: 3,
    name: pkg.name,
    version: pkg.version,
    icons: {
        48: 'public/logo.png',
    },
    action: {
        default_icon: {
            48: 'public/logo.png',
        },
        default_popup: 'src/popup/index.html',
    },
    permissions: [
        'activeTab',
        'storage',
        'tabs'
    ],
    "background": {
        "service_worker": "src/service-worker.js"
    },
    "host_permissions": [
        "http://localhost/*",
        "https://localhost/*",
        "https://tdb-swarm.int.alienor.net/*"
    ],
})
